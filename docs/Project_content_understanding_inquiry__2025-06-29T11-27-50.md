[ ] NAME:Current Task List DESCRIPTION:Root task for conversation f9b562c0-eae3-4d6b-858c-fd8b71535f87
-[x] NAME:请为 PomodoroTimer 应用的日历功能实现周视图和月视图，目前只有日视图。具体要求如下： DESCRIPTION:1. **实现范围**：    - 在现有的 CalendarView.swift 中添加周视图（Week View）和月视图（Month View）    - 保持与当前日视图的一致性和无缝切换  2. **设计要求**：    - 遵循 macOS 和 iOS 原生日历应用的设计规范和交互模式    - 确保在 macOS 和 iOS 平台上都有良好的用户体验    - 使用 SwiftUI 实现，保持与现有代码风格一致  3. **功能要求**：    - 周视图：显示一周7天，支持左右滑动切换周    - 月视图：显示完整月份网格，支持月份间导航    - 所有视图都应该显示番茄钟事件和活动监控数据    - 保持日期选择和事件显示的一致性  4. **技术要求**：    - 使用跨平台的 ActivityMonitorManager 获取数据    - 确保在 macOS 和 iOS 上都能正常编译和运行    - 保持现有的数据绑定和状态管理模式  5. **用户交互**：    - 提供视图切换控件（日/周/月）    - 支持手势导航和点击选择日期    - 保持选中日期在不同视图间的同步
-[x] NAME:日历功能下，在“日周月”的左侧加一个“今天”的按钮 DESCRIPTION:点击后，可以选中当天
-[x] NAME:日视图下，拖拽有些卡顿，体验优化 DESCRIPTION:
-[x] NAME:日视图下，鼠标点击选中一个区域的时候，这块区域的位置不对，没有和用户选中的区域位置保持一致，需要修复 DESCRIPTION:
-[x] NAME:日历功能下，右侧有一个空白栏目，应该删除掉 DESCRIPTION:
-[x] NAME:日视图下，多个任务并列效果不好，总宽度应该是整个日视图的宽度，但是现在这个宽度是固定死的，导致多个并列的任务很挤 DESCRIPTION:
-[x] NAME:日视图下，鼠标拖拽事件需要一段比较长距离后，事件才能被移动，导致不跟手，需要更好的体验 DESCRIPTION:
-[x] NAME:周视图下，多个事件时间范围有重叠，也需要并列 DESCRIPTION:
-[x] NAME:删除日历功能上方的左右切换按钮，只保留日期文字即可。删除日历功能上方的“视图模式”四个字 DESCRIPTION:
-[x] NAME:日视图右侧的日历模块怎么删除了，需要复原回来！！当选中一个事件的时候，还需要显示事件的详细信息啊！ DESCRIPTION:
-[x] NAME:点击日视图的其他位置，可以取消选中当前事件。日视图并列的总宽度限制还是不对啊，应该是日视图本身的总宽度 DESCRIPTION: